import { CommonModule } from '@angular/common';
import { BrowserModule } from '@angular/platform-browser';
import { AiRevisorComponent } from 'src/app/components/admin-layout/components/router/components/settings/ai-revisor/ai-revisor.component';
import { PiecesModule } from 'src/app/components/pieces/pieces.module';
import { MajorModule } from 'src/app/major.module';
import { AreaService } from 'src/app/services/area.service';
import { CharacterService } from 'src/app/services/character.service';
import { EmotionService } from 'src/app/services/emotion.service';
import { ItemService } from 'src/app/services/item.service';
import { LevelService } from 'src/app/services/level.service';
import { MissionService } from 'src/app/services/mission.service';
import { SceneryService } from 'src/app/services/scenery.service';
import { SoundService } from 'src/app/services/sound.service';
import { TutorialService } from 'src/app/services/tutorial.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { ThemeService } from 'src/app/services/video-theme.service';
import { VideoService } from 'src/app/services/video.service';
import { AudioListComponent } from './components/audio-list/audio-list.component';
import { MinionStatsComponent } from './components/minion-stats/minion-stats.component';
import { MissionListModule } from './components/mission-list/mission-list.module';
import { ReportViewModule } from './components/report-view/report-view.module';
import { RpgManagerModule } from './components/rpg-manager/rpg-manager.module';
import { SettingsModule } from './components/settings/settings.module';
import { StoryExpansionPackComponent } from './components/story-expansion-pack/story-expansion-pack.component';
import { NgModule } from '@angular/core';
import { LevelAndDialogueModule } from './components/level-and-dialogue/level-and-dialogue.module';
import { HttpClientModule } from '@angular/common/http';
import { FormsModule } from '@angular/forms';
import { AudioListenerModule } from 'src/app/components/audio-listener/audio-list.module';
import { ContentInputModule } from 'src/app/components/content-input/content-input.module';
import { PopupModule } from 'src/app/components/popup/popup.module';
import { AreaListComponent } from './components/area-list/area-list.component';
import { AudioUploaderComponent } from './components/audio-uploader/audio-uploader.component';
import { CharacterListComponent } from './components/character-list/character-list.component';
import { ChestListComponent } from './components/chest-list/chest-list.component';
import { ClassListComponent } from './components/class-list/class-lis/class-list.component';
import { EmotionListComponent } from './components/emotion-list/emotion-list.component';
import { LevelDialogueEditorComponent } from './components/level-and-dialogue/components/level-dialogue-editor/level-dialogue-editor.component';
import { LevelDialoguesManagerComponent } from './components/level-and-dialogue/components/level-dialogues-manager/level-dialogues-manager.component';
import { LevelListComponent } from './components/level-and-dialogue/components/level-list/level-list.component';
import { MissionListComponent } from './components/mission-list/mission-list.component';
import { ModalCharacterListComponent } from './components/modal-character-list/modal-character-list.component';
import { ParticleListComponent } from './components/particles-generator/particle-list/particle-list.component';
import { ReportViewComponent } from './components/report-view/report-view.component';
import { ReviewComponent } from './components/review/review.component';
import { RpgManagerComponent } from './components/rpg-manager/rpg-manager.component';
import { SceneryListComponent } from './components/scenery-list/scenery-list.component';
import { SearchResultsComponent } from './components/search-results/search-results.component';
import { SettingsComponent } from './components/settings/settings.component';
import { ThemeListComponent } from './components/theme-list/theme-list.component';
import { TutorialListComponent } from './components/tutorial-list/tutorial-list.component';
import { VideoListComponent } from './components/video-list/video-list.component';
import { WeaponRarityComponent } from './components/weapon-rarity/weapon-rarity.component';

import { LevelDialogueEditorResolve } from './components/level-and-dialogue/components/level-dialogue-editor/level-dialogue-editor.resolve';
import { LevelDialoguesManagerResolve } from './components/level-and-dialogue/components/level-dialogues-manager/level-dialogues-manager.resolve';

import { EffectService, KeywordService, ParticleService, PassiveService, SoulsGrinderService, WeaponService, WeaponUpgradeService } from 'src/app/services';
import { CodeBlockDropService } from 'src/app/services/code-block-drop.service';
import { CustomService } from 'src/app/services/custom.service';
import { DropService } from 'src/app/services/drop.service';
import { ItemClassService } from 'src/app/services/item-class.service';
import { MemoryModuleService } from 'src/app/services/memorymodule.service';
import { MicroloopContainerService } from 'src/app/services/microloop-container.service';
import { MicroloopService } from 'src/app/services/microloop.service';
import { PowerUpService } from 'src/app/services/powerup.service';
import { PowerUpStatService } from 'src/app/services/powerupstat.service';
import { AreaDropsGeneratorComponent } from './components/battle-drops-generator/area-drops-generator/area-drops-generator.component';
import { BattleDropsGeneratorComponent } from './components/battle-drops-generator/battle-drops-generator.component';
import { CodeBlockDropsGeneratorComponent } from './components/battle-drops-generator/code-block-drops-generator/code-block-drops-generator';
import { DropVarianceAComponent } from './components/battle-drops-generator/drop-varianceA/drop-varianceA.component';
import { DropVarianceBComponent } from './components/battle-drops-generator/drop-varianceB/drop-varianceB.component';
import { IngredientDropComponent } from './components/battle-drops-generator/ingredient-drop/ingredient-drop.component';
import { IngredientVarianceComponent } from './components/battle-drops-generator/ingredient-variance/ingredient-variance.component';
import { OtherDropsGeneratorComponent } from './components/battle-drops-generator/other-drops-generator/other-drops-generator';
import { ParticleDropComponent } from './components/battle-drops-generator/particle-drop/particle-drop.component';
import { ParticleVarianceComponent } from './components/battle-drops-generator/particle-variance/particle-variance.component';
import { BoundItemListComponent } from './components/bound-item-list/bound-item-list.component';
import { BuildingGeneratorComponent } from './components/building-generator/building-generator.component';
import { MiningGeneratorComponent } from './components/building-generator/mining-generator/mining-generator.component';
import { CollectibleRarityComponent } from './components/collectible-rarity/collectible-rarity.component';
import { EffectGeneratorComponent } from './components/effect-generator/effect-generator.component';
import { ItemClassListComponent } from './components/item-class-list/item-class-list.component';
import { ItemDetailsClassSelectionComponent } from './components/item-details/item-details-class-selection/item-details-class-selection.component';
import { ItemDetailsItemSelectionComponent } from './components/item-details/item-details-item-selection/item-details-item-selection.component';
import { ItemDetailsReportGeneralComponent } from './components/item-details/item-details-report/item-details-report-general/item-details-report-general.component';
import { ItemDetailsReportImproveComponent } from './components/item-details/item-details-report/item-details-report-improve/item-details-report-improve.component';
import { ItemDetailsReportPassiveComponent } from './components/item-details/item-details-report/item-details-report-passive/item-details-report-passive.component';
import { ItemDetailsReportComponent } from './components/item-details/item-details-report/item-details-report.component';
import { ItemDetailsComponent } from './components/item-details/item-details.component';
import { KeywordsListComponent } from './components/keywords-list/keywords-list.component';
import { LaboratoriesGeneratorComponent } from './components/laboratory/laboratories-generator.component';
import { LanguageListComponent } from './components/language-list/language-list.component';
import { MicroloopDialogueEditorResolve } from './components/level-and-dialogue/components/level-dialogue-editor/microloop-dialogue-editor.resolve';
import { MemoryModuleClassSelectionComponent } from './components/memorymodule-generator/memorymodule-class-selection/memorymodule-class-selection.component';
import { MemoryModuleGeneratorComponent } from './components/memorymodule-generator/memorymodule-generator.component';
import { MemoryModuleInformationComponent } from './components/memorymodule-generator/memorymodule-information/memorymodule-information.component';
import { MemoryModuleSelectionComponent } from './components/memorymodule-generator/memorymodule-selection/memorymodule-selection.component';
import { MicroloopListComponent } from './components/microloop-list/microloop-list.component';
import { MicroloopDialoguesManagerComponent } from './components/microloop/microloop-dialogue/microloop-dialogues-manager/microloop-dialogues-manager.component';
import { MicroloopDialoguesManagerResolve } from './components/microloop/microloop-dialogue/microloop-dialogues-manager/microloop-dialogues-manager.resolve';
import { MicroloopComponent } from './components/microloop/microloop.component';
import { MinigameComponent } from './components/minigame/minigame.component';
import { OthersComponent } from './components/others/others.component';
import { ParticleClassSelectionComponent } from './components/particles-generator/particle-class-selection/particle-class-selection.component';
import { ParticleInformationComponent } from './components/particles-generator/particle-information/particle-information.component';
import { ParticleItemSelectionComponent } from './components/particles-generator/particle-item-selection/particle-item-selection.component';
import { ParticlesGeneratorComponent } from './components/particles-generator/particles-generator.component';
import { PassiveGeneratorComponent } from './components/passive-generator/passive-generator.component';
import { PowerupClassSelectionComponent } from './components/powerup-generator/powerup-class-selection/powerup-class-selection.component';
import { PowerupGeneratorComponent } from './components/powerup-generator/powerup-generator.component';
import { PowerupInformationComponent } from './components/powerup-generator/powerup-information/powerup-information.component';
import { PowerupSelectionComponent } from './components/powerup-generator/powerup-selection/powerup-selection.component';
import { CurrencyReportResolve } from './components/report-view/components/currency-report/currency-report.resolve';
import { LevelDialogueReportResolve } from './components/report-view/components/level-dialogue-report/level-dialogue-report.resolve';
import { LevelPinReportResolve } from './components/report-view/components/level-pin-report/level-pin.component.resolve';
import { LevelReportResolve } from './components/report-view/components/level-report/level-report.resolve';
import { CharacterSelector } from './components/selectors/character-selector/character-selector.component';
import { ClassSelector } from './components/selectors/class-selector/class-selector.component';
import { EnabledCharacterSelector } from './components/selectors/enabled-character-selector/enabled-character-selector.component';
import { EnabledClassSelector } from './components/selectors/enabled-class-selector/enabled-class-selector.component';
import { SpecialWeaponClassSelectionComponent } from './components/special-weapon-generator/special-weapon-class-selection/special-weapon-class-selection.component';
import { SpecialWeaponGeneratorComponent } from './components/special-weapon-generator/special-weapon-generator.component';
import { SpecialWeaponInformationComponent } from './components/special-weapon-generator/special-weapon-information/special-weapon-information.component';
import { LevelUpgradeComponent } from './components/stats/level-upgrades/level-upgrades.component';
import { LevelXPComponent } from './components/stats/level-xp/level-xp.component';
import { StatsComponent } from './components/stats/stats.component';
import { TabListComponent } from './components/tab-list/tab-list.component';
import { TagListComponent } from './components/tag-list/tag-list.component';
import { TierListComponent } from './components/tier-list/tier-list.component';
import { UpgradesClassSelectionComponent } from './components/upgrades-generator/upgrades-class-selection/upgrades-class-selection.component';
import { UpgradesGeneratorComponent } from './components/upgrades-generator/upgrades-generator.component';
import { UpgradesInformationComponent } from './components/upgrades-generator/upgrades-information/upgrades-information.component';

import { AdamantiumMiningService } from 'src/app/services/adamantium-mining.service';
import { AdamantiumStorageService } from 'src/app/services/adamantium-storage.service';
import { BlueprintArchiveService } from 'src/app/services/blueprint-archive.service';
import { CollectibleRarityCodeBlocksService } from 'src/app/services/collectibleRarityCodeBlocks.service';
import { CollectibleRarityGoldService } from 'src/app/services/collectibleRarityGold.service';
import { CollectibleRaritySoulsService } from 'src/app/services/collectibleRaritySouls.service';
import { HellniumMiningService } from 'src/app/services/hellnium-mining.service';
import { HellniumStorageService } from 'src/app/services/hellnium-storage.service';
import { LaboratoryService } from 'src/app/services/laboratory.service';
import { ProfanariumService } from 'src/app/services/profanarium.service';
import { SpecialWeaponService } from 'src/app/services/special-weapon.service';
import { TitaniumMiningService } from 'src/app/services/titanium-mining.service';
import { TitaniumStorageService } from 'src/app/services/titanium-storage.service';

import { BlueprintArchiveGeneratorComponent } from './components/building-generator/blueprint-archive-generator/blueprint-archive-generator.component';
import { LaboratoryGeneratorComponent } from './components/building-generator/laboratory-generator/laboratory-generator.component';
import { AdamantiumGeneratorComponent } from './components/building-generator/mining-generator/adamantium-generator/adamantium-generator.component';
import { TitaniumGeneratorComponent } from './components/building-generator/mining-generator/titanium-generator/titanium-generator.component';

import { TitaniumMiningGeneratorComponent } from './components/building-generator/mining-generator/titanium-generator/titanium-mining-generator/titanium-mining-generator.component';
import { TitaniumStorageGeneratorComponent } from './components/building-generator/mining-generator/titanium-generator/titanium-storage-generator/titanium-storage-generator.component';
import { TitaniumStorageBGeneratorComponent } from './components/building-generator/mining-generator/titanium-generator/titanium-storageB-generator/titanium-storageB-generator.component';
import { TitaniumStorageCGeneratorComponent } from './components/building-generator/mining-generator/titanium-generator/titanium-storageC-generator/titanium-storageC-generator.component';

import { AdamantiumMiningGeneratorComponent } from './components/building-generator/mining-generator/adamantium-generator/adamantium-mining-generator/adamantium-mining-generator.component';
import { AdamantiumStorageGeneratorComponent } from './components/building-generator/mining-generator/adamantium-generator/adamantium-storage-generator/adamantium-storage-generator.component';
import { AdamantiumStorageBGeneratorComponent } from './components/building-generator/mining-generator/adamantium-generator/adamantium-storageB-generator/adamantium-storageB-generator.component';
import { AdamantiumStorageCGeneratorComponent } from './components/building-generator/mining-generator/adamantium-generator/adamantium-storageC-generator/adamantium-storageC-generator.component';

import { HellniumGeneratorComponent } from './components/building-generator/mining-generator/hellnium-generator/hellnium-generator.component';
import { HellniumMiningGeneratorComponent } from './components/building-generator/mining-generator/hellnium-generator/hellnium-mining-generator/hellnium-mining-generator.component';
import { HellniumStorageGeneratorComponent } from './components/building-generator/mining-generator/hellnium-generator/hellnium-storage-generator/hellnium-storage-generator.component';
import { HellniumStorageBGeneratorComponent } from './components/building-generator/mining-generator/hellnium-generator/hellnium-storageB-generator/hellnium-storageB-generator.component';
import { HellniumStorageCGeneratorComponent } from './components/building-generator/mining-generator/hellnium-generator/hellnium-storageC-generator/hellnium-storageC-generator.component';


import { SoulsGrinderGeneratorComponent } from './components/building-generator/souls-grinder-generator/souls-grinder-generator.component';
import { SoulsGrinderStorageAComponent } from './components/building-generator/souls-grinder-generator/souls-grinder-storageA/souls-grinder-storageA.component';
import { SoulsGrinderStorageBComponent } from './components/building-generator/souls-grinder-generator/souls-grinder-storageB/souls-grinder-storageB.component';
import { SoulsGrinderStorageCComponent } from './components/building-generator/souls-grinder-generator/souls-grinder-storageC/souls-grinder-storageC.component';
import { SoulsGrinderComponent } from './components/building-generator/souls-grinder-generator/souls-grinder/souls-grinder.component';

import { ProfanariumGeneratorComponent } from './components/building-generator/profanarium-generator/profanarium-generator.component';
import { CollectibleCostsComponent } from './components/collectible-costs/collectible-costs.component';
import { UpgradeCostsCodeBlocksComponent } from './components/collectible-costs/collectibles/code-blocks/upgrade-costs-code-blocks.component';
import { UpgradeCostsGoldComponent } from './components/collectible-costs/collectibles/gold/upgrade-costs-gold.component';
import { UpgradeCostsSoulsComponent } from './components/collectible-costs/collectibles/souls/upgrade-costs-souls.component';
import { UpgradeCostsComponent } from './components/collectible-costs/collectibles/upgrade-costs.component';

import { StatusService } from 'src/app/services/status.service';
import { MasteryComponent } from './components/mastery/mastery.component';
import { ModifierListComponent } from './components/modifier-list/modifier-list.component';
import { AIComponent } from './components/settings/ai-prompt/ai-prompt.component';
import { AnimationsSelectorComponent } from './components/settings/separation/animation/animations-selector.component';
import { CharactersSelectorComponent } from './components/settings/separation/characters/characters-selector.component';
import { ItemsSelectorComponent } from './components/settings/separation/items/items-selector.component';
import { StatusEffectComponent } from './components/status-effect/status-effect.component';
import { StatusListComponent } from './components/status-list/status-list.component';

import { PrimalModifierService } from 'src/app/services/primal-modifier.service';
import { StatusInfoService } from 'src/app/services/status-info.service';

import { BattleUpgradeComponent } from './components/collectible-details/battle-upgrade/battle-upgrade.component';
import { CollectibleDetailsComponent } from './components/collectible-details/collectible-details.component';
import { PrimalModifierComponent } from './components/collectible-details/primal-modifier/primal-modifier.component';
import { StatusInfoComponent } from './components/collectible-details/status-info/status-info.component';

import { KeywordTagComponent } from './components/keywords-tags/keyword-tag.component';
import { MapsComponent } from './components/maps/maps.component';
import { SilicatosComponent } from './components/silicates-generator/silicatos/silicatos.component';

import { LoadingSpinnerComponent } from 'src/app/loading-spinner/loading-spinner.component';
import { WeaponRarityService } from 'src/app/services/WeaponRarity.service';
import { SpinnerComponent } from 'src/app/spinner/spinner.component';
import { AilmentDefensesComponent } from './components/ailment-defenses/ailment-defenses.component';
import { AilmentComponent } from './components/ailment/ailment.component';
import { AtributteComponent } from './components/atributte/atributte.component';
import { SubContextComponent } from './components/atributte/sub-context/sub-context.component';
import { ReportsHCExcelComponent } from './components/battle-drops-generator/reports-hcexcel/reports-hcexcel.component';
import { ClassModifierComponent } from './components/class-list/class-modifier/class-modifier.component';
import { ClassesComponent } from './components/class-list/classes.component';
import { RelicUserComponent } from './components/class-list/relicUses/relicUses.component';
import { BattleInferiorComponent } from './components/collectible-details/battle-inferior/battle-inferior.component';
import { SpecialSkillsComponent } from './components/collectible-details/special-skills/special-skills.component';
import { AtributteDiceFrustrationComponent } from './components/dice-frustration/attributce-dice-frustration/attribute-dice-frustration.component';
import { FailureLevelsComponent } from './components/dice-frustration/failure-levels.component';
import { ElementalDefensesComponent } from './components/elemental-defenses/elemental-defenses.component';
import { MahankaraBehaviorTableComponent } from './components/mahankara-behavior-table/mahankara-behavior-table.component';
import { MahankaraCategoriesXStressStatesComponent } from './components/mahankara-categories-x-stress-states/mahankara-categories-x-stress-states.component';
import { MahankaraCategoriesComponent } from './components/mahankara-categories/mahankara-categories.component';
import { MahankaraConcatenationsComponent } from './components/mahankara-concatenations/mahankara-concatenations.component';
import { MahankaraDialogsComponent } from './components/mahankara-dialogs/mahankara-dialogs.component';
import { MahankaraGroupingsComponent } from './components/mahankara-groupings/mahankara-groupings.component';
import { BonusComponent } from './components/passive-atributes/bonus/bonus.component';
import { ConditionComponent } from './components/passive-atributes/condition/condition.component';
import { DurationComponent } from './components/passive-atributes/duration/duration.component';
import { PassiveAllowedComponent } from './components/passive-atributes/passive-allowed/passive-allowed.component';
import { PassiveAtributesComponent } from './components/passive-atributes/passive-atributes.component';
import { CleanOrphansComponent } from './components/settings/clean-orphans/clean-orphans.component';
import { DCGuideComponent } from './components/settings/dc-guide/dc-guide.component';
import { SilicateClassSelectionComponent } from './components/silicates-generator/silicate-class-selection/silicate-class-selection.component';
import { SilicateGeneratorComponent } from './components/silicates-generator/silicate-generator.component';
import { ConfigThresholdComponent } from './components/stats/config-threshold/config-threshold.component';
import { CategoryStatusEffectComponent } from './components/status-effect/category-status-effect/category-status-effect.component';
import { AilmentIdBlocksComponent } from './components/status-effect/idBlocks/ailment-id-blocks/ailment-id-blocks.component';
import { BoostIdBlocksComponent } from './components/status-effect/idBlocks/boost-id-blocks/boost-id-blocks.component';
import { DefensiveIdBlocksComponent } from './components/status-effect/idBlocks/defensive-id-blocks/defensive-id-blocks.component';
import { DispelIdBlocksComponent } from './components/status-effect/idBlocks/dispel-id-blocks/dispel-id-blocks.component';
import { HealingIdBlocksComponent } from './components/status-effect/idBlocks/healing-id-blocks/healing-id-blocks.component';
import { NegativeIdBlocksComponent } from './components/status-effect/idBlocks/negative-id-blocks/negative-id-blocks.component';
import { RepetitionStatusEffectComponent } from './components/status-effect/repetition-status-effect/repetition-status-effect.component';
import { AfflictionTableComponent } from './components/status-effect/status-effects-tables/affliction-table/affliction-table.component';
import { AilmentTableComponent } from './components/status-effect/status-effects-tables/ailment-table/ailment-table.component';
import { BoostTableComponent } from './components/status-effect/status-effects-tables/boost-table/boost-table.component';
import { DefensiveTableComponent } from './components/status-effect/status-effects-tables/defensive-table/defensive-table.component';
import { DispelTableComponent } from './components/status-effect/status-effects-tables/dispel-table/dispel-table.component';
import { HealingTableComponent } from './components/status-effect/status-effects-tables/healing-table/healing-table.component';
import { HybridTableComponent } from './components/status-effect/status-effects-tables/hybrid-table/hybrid-table.component';
import { NegativeTableComponent } from './components/status-effect/status-effects-tables/negative-table/negative-table.component';
import { CommonWeaponsComponent } from './components/weapons-wlbase/cammon-weapons/common-weapons.component';
import { SpecialWeaponsComponent } from './components/weapons-wlbase/special-weapons/special-weapons.component';
import { WeaponsWLBaseComponent } from './components/weapons-wlbase/weapons-wlbase.component';
import { KnowledgeComponent } from './components/knowledge/knowledge.component';
import { SubContextKnowledgeComponent } from './components/knowledge/sub-context-knowledge/sub-context-knowledge.component';
import { SituationalModifierComponent } from './components/class-list/situational-modifier/situational-modifier.component';
import { DcKnowledgeGuideComponent } from './components/settings/dc-knowledge-guide/dc-knowledge-guide.component';
import { DcAttributeGuideComponent } from './components/settings/dc-attribute-guide/dc-attribute-guide.component';
import { AttributeCheckComponent } from './components/dice-frustration/attribute-check/attribute-check.component';
import { KnowledgeCheckComponent } from './components/dice-frustration/knowledge-check/knowledge-check.component';
import { KnowledgeDiceFrustrationComponent } from './components/dice-frustration/knowledge-dice-frustration/knowledge-dice-frustration.component';
import { ArchetypesListComponent } from './components/class-list/archetypes-list/archetypes-list.component';
import { ReportArchetypeComponent } from './components/battle-drops-generator/reports-hcexcel/report-archetype/report-archetype.component';
import { SkillTreeComponent } from './components/skill-tree/skill-tree.component';
import { GoldenComponent } from './components/skill-tree/golden/golden.component';
import { SoulsComponent } from './components/skill-tree/souls/souls.component';
import { SigilosComponent } from './components/skill-tree/sigilos/sigilos.component';
import { AmplifiersComponent } from './components/amplifiers/amplifiers.component';
import { CtrEvMaxComponent } from './components/collectible-rarity/ctr-ev-max/ctr-ev-max.component';
import { CtrCollectibleComponent } from './components/collectible-details/ctr-collectible/ctr-collectible.component';
import { LukCollectibleComponent } from './components/collectible-details/luk-collectible/luk-collectible.component';
import { IntCollectibleComponent } from './components/collectible-details/int-collectible/int-collectible.component';
import { SpdCollectibleComponent } from './components/collectible-details/spd-collectible/spd-collectible.component';
import { RouterModule, Routes } from '@angular/router';
import { OpenAiEnvironmentComponent } from './components/settings/open-ai-environment/open-ai-environment.component';
import { HealingChangeComponent } from './components/healing-change/healing-change.component';
import { ComboComponent } from './components/status-effect/status-effects-tables/combo/combo.component';
import { ModalInfoSpecialSkillsComponent } from './components/collectible-details/special-skills/modal-info-special-skills/modal-info-special-skills.component';
import { MissionNotesComponent } from './components/mission-list/components/mission-notes/mission-notes.component';
import { AiRevisorModalComponent } from './components/settings/ai-revisor/ai-revisor-modal/ai-revisor-modal.component';
import { ChaosComponent } from './components/status-effect/idBlocks/chaos/chaos.component';
import { ChaosTableComponent } from './components/status-effect/status-effects-tables/chaos-table/chaos-table.component';
import { RpcComponent } from './components/rpc/rpc.component';
import { MODMoonAttributesComponent } from './components/class-list/modMoon-Attributes/modMoon-attributes.component';
import { ModMoonRangesComponent } from './components/class-list/modMoon-ranges/modMoon-ranges.component';
import { ParryPryComponent } from './components/class-list/parry-pry/parry-pry.component';
import { DestinyDiceComponent } from './components/destiny-dice/destiny-dice.component';
import { ViewSendModalComponent } from './components/settings/ai-revisor/view-send-modal/view-send-modal.component';



export const routes: Routes = [
  // Redireciona a rota inicial para 'settings'
  { path: '', redirectTo: 'settings', pathMatch: 'full' },

  {
    path: 'settings',
    component: SettingsComponent,
    resolve: { preloadedData: UserSettingsService },
  },
  {
    path: 'audios',
    component: AudioListComponent,
    resolve: { preloadedData: SoundService },
  },
  { path: 'search', component: SearchResultsComponent },
  {
    path: 'report',
    component: ReportViewComponent,
    resolve: {
      preloadedCurrencyReportData: CurrencyReportResolve,
      preloadedLevelDialogueReportData: LevelDialogueReportResolve,
      preloadedLevelReportResolveData: LevelReportResolve,
      preloadedLevelPinReportData: LevelPinReportResolve,
    },
  },
  { path: 'review', component: ReviewComponent },
  { path: 'rpg', component: RpgManagerComponent },
  {
    path: 'ai-revisor',
    component: AiRevisorComponent,
  },
  {
    path: 'areas',
    component: AreaListComponent,
    resolve: { preloadedData: AreaService },
  },
  {
    path: 'particles',
    component: ParticleListComponent,
    resolve: { preloadedData: AreaService },
  },
  {
    path: 'levels',
    component: LevelListComponent,
    resolve: { preloadedData: LevelService },
  },
  {
    path: 'levels/:levelId/dialogues',
    component: LevelDialoguesManagerComponent,
    resolve: {
      preloadedData: LevelDialoguesManagerResolve,
    },
  },
  {
    path: 'levels/:levelId/dialogues/:dialogueId',
    component: LevelDialogueEditorComponent,
    resolve: {
      preloadedData: LevelDialogueEditorResolve,
    },
  },
  {
    path: 'sceneries',
    component: SceneryListComponent,
    resolve: { preloadedData: SceneryService },
  },
  {
    path: 'characters',
    component: CharacterListComponent,
    resolve: { preloadedData: CharacterService },
  },
  {
    path: 'ModalCharacterList',
    component: ModalCharacterListComponent,
  },
  {
    path: 'classes',
    component: ClassesComponent,
  },
  {
    path: 'emotions',
    component: EmotionListComponent,
    resolve: { preloadedData: EmotionService },
  },
  {
    path: 'audioUploader',
    component: AudioUploaderComponent,
  },
  {
    path: 'missions',
    component: MissionListComponent,
    resolve: { preloadedData: MissionService },
  },
  {
    path: 'videos',
    component: VideoListComponent,
    resolve: { preloadedData: VideoService },
  },
  {
    path: 'weaponRarity',
    component: WeaponRarityComponent,
    resolve: { preloadedData: WeaponRarityService },
  },
  {
    path: 'themes',
    component: ThemeListComponent,
    resolve: { preloadedData: ThemeService },
  },
  {
    path: 'tutorials',
    component: TutorialListComponent,
    resolve: { preloadedData: TutorialService },
  },
  {
    path: 'minigames',
    component: MinigameComponent,
  },
  {
    path: 'itemClass',
    component: ItemClassListComponent,
  },
  {
    path: 'boundItems',
    component: BoundItemListComponent,
    resolve: {
      preloadedData: ItemService, ItemClassService,
    },
  },
  {
    path: 'weaponRecords',
    component: ItemDetailsComponent,
    resolve: {
      preloadedData: PassiveService, WeaponUpgradeService, WeaponService, CustomService,
    },
  },
  {
    path: 'weaponRecord',
    component: ItemDetailsReportComponent,
    resolve: {
      preloadedData: PassiveService, WeaponUpgradeService, WeaponService, CustomService, EffectService,
    },
  },
  {
    path: 'collectibleRecord',
    component: CollectibleDetailsComponent,
  },
  {
    path: 'tags',
    component: TagListComponent,
  },
  {
    path: 'missionNotes',
    component: MissionNotesComponent,
  },
  {
    path: 'missions',
    component: MissionListComponent,
  },
  {
    path: 'tabs',
    component: TabListComponent,
  },
  {
    path: 'keywordsTags',
    component: KeywordTagComponent,
  },
  {
    path: 'archetypesList',
    component: ArchetypesListComponent,
  },
  {
    path: 'microloops',
    component: MicroloopComponent,
    resolve: { preloadedData: MicroloopService },
  },
  {
    path: 'microloops/:levelId/dialogues',
    component: MicroloopDialoguesManagerComponent,
    resolve: {
      preloadedData: MicroloopDialoguesManagerResolve,
    },
  },
  {
    path: 'microloops/:levelId/dialogues/:dialogueId',
    component: LevelDialogueEditorComponent,
    resolve: {
      preloadedData: MicroloopDialogueEditorResolve,
    },
  },
  {
    path: 'microloopList',
    component: MicroloopListComponent,
    resolve: {
      preloadedData: MicroloopContainerService,
    },
  },
  {
    path: 'microloopList/:areaId/microloops',
    component: MicroloopComponent,
    resolve: {
      preloadedData: MicroloopService, MicroloopContainerService,
    },
  },
  {
    path: 'language',
    component: LanguageListComponent,
  },
  {
    path: 'stats',
    component: StatsComponent,
  },
  {
    path: 'skillTree',
    component: SkillTreeComponent,
  },
  {
    path: 'others',
    component: OthersComponent,
    resolve: {
      preLoadedData: PassiveService, TutorialService, KeywordService, EffectService, CharacterService, AreaService, CollectibleRarityGoldService,
      CollectibleRarityCodeBlocksService, CollectibleRaritySoulsService, StatusService, PrimalModifierService, StatusInfoService,
    },
  },
  {
    path: 'passiveGenerator',
    component: PassiveGeneratorComponent,
    resolve: {
      preLoadedData: PassiveService,
    },
  },
  {
    path: 'particlesGenerator',
    component: ParticlesGeneratorComponent,
    resolve: {
      preLoadedData: ParticleService,
    },
  },
  {
    path: 'particleInformation',
    component: ParticleInformationComponent,
    resolve: {
      preLoadedData: ParticleService, WeaponService, EffectService, CustomService,
    },
  },
  {
    path: 'powerupGenerator',
    component: PowerupGeneratorComponent,
    resolve: {
      preLoadedData: PowerUpService, CustomService, ParticleService, ItemService, ItemClassService, PowerUpStatService,
    },
  },
  {
    path: 'powerupInformation',
    component: PowerupInformationComponent,
    resolve: {
      preLoadedData: PowerUpService, CustomService, ParticleService, ItemService, ItemClassService, PowerUpStatService,
    },
  },
  {
    path: 'memoryModuleGenerator',
    component: MemoryModuleGeneratorComponent,
    resolve: {
      preLoadedData: MemoryModuleService, CustomService, ParticleService, ItemService, ItemClassService,
    },
  },
  {
    path: 'effectGenerator',
    component: EffectGeneratorComponent,
    resolve: {
      preLoadedData: EffectService,
    },
  },
  {
    path: 'battleDropsGenerator',
    component: BattleDropsGeneratorComponent,
    resolve: {
      preLoadedData: CodeBlockDropService, DropService,
    },
  },
  {
    path: 'charactersSelector',
    component: CharactersSelectorComponent,
  },
  {
    path: 'aiPrompt',
    component: AIComponent,
  },
  {
    path: 'openAiEnvironment',
    component: OpenAiEnvironmentComponent,
  },
  {
    path: 'dcGuide',
    component: DCGuideComponent,
  },
  {
    path: 'cleanOrphans',
    component: CleanOrphansComponent,
  },
  {
    path: 'animationsSelector',
    component: AnimationsSelectorComponent,
  },
  {
    path: 'itemsSelector',
    component: ItemsSelectorComponent,
  },
  {
    path: 'otherDropsGenerator',
    component: OtherDropsGeneratorComponent,
    resolve: {
      preLoadedData: CodeBlockDropService, DropService,
    },
  },
  {
    path: 'upgrades',
    component: UpgradesGeneratorComponent,
  },
  {
    path: 'reportsHCExcel',
    component: ReportsHCExcelComponent,
  },
  {
    path: 'specialWeaponGenerator',
    component: SpecialWeaponGeneratorComponent,
    resolve: {
      preLoadedData: SpecialWeaponService, CustomService, ItemService, ItemClassService,
    },
  },
  {
    path: 'buildingGenerator',
    component: BuildingGeneratorComponent,
    resolve: {
      preLoadedData: LaboratoryService, TitaniumMiningService, TitaniumStorageService, BlueprintArchiveService,
      AdamantiumMiningService, AdamantiumStorageService, HellniumMiningService, HellniumStorageService, SoulsGrinderService,
      ProfanariumService,
    },
  },
  {
    path: 'laboratories',
    component: LaboratoriesGeneratorComponent,
    resolve: {
      preLoadedData: LaboratoryService, TitaniumMiningService, TitaniumStorageService, BlueprintArchiveService,
      AdamantiumMiningService, AdamantiumStorageService, HellniumMiningService, HellniumStorageService, SoulsGrinderService,
      ProfanariumService,
    },
  },
  // Rota coringa (sempre no final)
  { path: '**', redirectTo: 'settings', pathMatch: 'full' },
];


export const routingComponents = [

  AilmentComponent,
  AilmentDefensesComponent,
  CollectibleCostsComponent,
  DCGuideComponent,
  ProfanariumGeneratorComponent,
  SoulsGrinderGeneratorComponent,
  SoulsGrinderComponent,
  SoulsGrinderStorageAComponent,
  SoulsGrinderStorageBComponent,
  SoulsGrinderStorageCComponent,
  AreaListComponent,
  SceneryListComponent,
  CharacterListComponent,
  ClassesComponent,
  ClassModifierComponent,
  ModalCharacterListComponent,
  ClassListComponent,
  EmotionListComponent,
  AudioUploaderComponent,
  ParticleListComponent,
  VideoListComponent,
  WeaponRarityComponent,
  TutorialListComponent,
  KeywordsListComponent,
  ThemeListComponent,
  SearchResultsComponent,
  ReviewComponent,
  AudioListComponent,
  MinigameComponent,
  TierListComponent,
  ItemClassListComponent,
  BoundItemListComponent,
  ItemDetailsComponent,
  ItemDetailsClassSelectionComponent,
  ItemDetailsItemSelectionComponent,
  ItemDetailsReportComponent,
  ItemDetailsReportGeneralComponent,
  ItemDetailsReportImproveComponent,
  ItemDetailsReportPassiveComponent,
  RelicUserComponent,
  SilicateGeneratorComponent,
  CharactersSelectorComponent,
  AIComponent,
  AnimationsSelectorComponent,
  AiRevisorComponent,
  ItemsSelectorComponent,
  CharacterSelector,
  EnabledCharacterSelector,
  ReportsHCExcelComponent,
  ClassSelector,
  EnabledClassSelector,
  PassiveGeneratorComponent,
  ParticlesGeneratorComponent,
  SilicateClassSelectionComponent,
  ParticleClassSelectionComponent,
  ParticleItemSelectionComponent,
  ParticleInformationComponent,
  PowerupGeneratorComponent,
  PowerupClassSelectionComponent,
  PowerupInformationComponent,
  PowerupSelectionComponent,
  MemoryModuleClassSelectionComponent,
  MemoryModuleGeneratorComponent,
  MemoryModuleSelectionComponent,
  MemoryModuleInformationComponent,
  EffectGeneratorComponent,
  CollectibleRarityComponent,
  TagListComponent,
  TabListComponent,
  MicroloopComponent,
  MicroloopDialoguesManagerComponent,
  MicroloopListComponent,
  LanguageListComponent,
  StatsComponent,
  OthersComponent,
  LevelUpgradeComponent,
  LevelXPComponent,
  BattleDropsGeneratorComponent,
  CodeBlockDropsGeneratorComponent,
  OtherDropsGeneratorComponent,
  AreaDropsGeneratorComponent,
  DropVarianceAComponent,
  DropVarianceBComponent,
  SpecialWeaponGeneratorComponent,
  UpgradesGeneratorComponent,
  SpecialWeaponClassSelectionComponent,
  UpgradesClassSelectionComponent,
  SpecialWeaponInformationComponent,
  UpgradesInformationComponent,
  BuildingGeneratorComponent,
  LaboratoriesGeneratorComponent,
  LaboratoryGeneratorComponent,
  TitaniumGeneratorComponent,
  AdamantiumGeneratorComponent,
  BlueprintArchiveGeneratorComponent,

  TitaniumMiningGeneratorComponent,
  TitaniumStorageGeneratorComponent,
  TitaniumStorageBGeneratorComponent,
  TitaniumStorageCGeneratorComponent,

  AdamantiumMiningGeneratorComponent,
  AdamantiumStorageGeneratorComponent,
  AdamantiumStorageBGeneratorComponent,
  AdamantiumStorageCGeneratorComponent,

  HellniumMiningGeneratorComponent,
  HellniumStorageGeneratorComponent,
  HellniumStorageBGeneratorComponent,
  HellniumStorageCGeneratorComponent,

  MiningGeneratorComponent,
  HellniumGeneratorComponent,
  UpgradeCostsGoldComponent,
  UpgradeCostsComponent,

  UpgradeCostsCodeBlocksComponent,
  UpgradeCostsSoulsComponent,
  StatusListComponent,
  StatusEffectComponent,
  ModifierListComponent,
  AtributteComponent,
  ElementalDefensesComponent,
  MasteryComponent,
  CollectibleDetailsComponent,
  StatusInfoComponent,
  PrimalModifierComponent,
  BattleUpgradeComponent,
  SilicatosComponent,
  SpinnerComponent,

  KeywordTagComponent,
  MapsComponent,
  StoryExpansionPackComponent,
  MinionStatsComponent,
  ParticleDropComponent,
  IngredientDropComponent,
  IngredientVarianceComponent,
  ChestListComponent,
  ParticleVarianceComponent,
  LoadingSpinnerComponent,
  WeaponsWLBaseComponent,
  CommonWeaponsComponent,
  SpecialWeaponsComponent,
  PassiveAtributesComponent,
  BonusComponent,
  ConditionComponent,
  DurationComponent,
  PassiveAllowedComponent,
  BattleInferiorComponent,
  ConfigThresholdComponent,
  CategoryStatusEffectComponent,
  RepetitionStatusEffectComponent,
  AilmentIdBlocksComponent,
  BoostIdBlocksComponent,
  DefensiveIdBlocksComponent,
  DispelIdBlocksComponent,
  HealingIdBlocksComponent,
  NegativeIdBlocksComponent,
  AfflictionTableComponent,
  AilmentTableComponent,
  BoostTableComponent,
  DefensiveTableComponent,
  DispelTableComponent,
  HealingTableComponent,
  HybridTableComponent,
  NegativeTableComponent,
  SpecialSkillsComponent,
  MahankaraDialogsComponent,
  MahankaraBehaviorTableComponent,
  MahankaraCategoriesComponent,
  MahankaraGroupingsComponent,
  MahankaraConcatenationsComponent,
  MahankaraCategoriesXStressStatesComponent,
  SubContextComponent,
  FailureLevelsComponent,
  AtributteDiceFrustrationComponent,
  CleanOrphansComponent,
  KnowledgeComponent,
  SubContextKnowledgeComponent,
  SituationalModifierComponent,
  DcKnowledgeGuideComponent,
  DcAttributeGuideComponent,
  AttributeCheckComponent,
  KnowledgeCheckComponent,
  KnowledgeDiceFrustrationComponent,
  ArchetypesListComponent,
  ReportArchetypeComponent, 
  SkillTreeComponent,
  GoldenComponent,
  SoulsComponent,
  SigilosComponent,
  AmplifiersComponent,
  CtrEvMaxComponent,
  CtrCollectibleComponent,
  LukCollectibleComponent, 
  IntCollectibleComponent,
  SpdCollectibleComponent,
  OpenAiEnvironmentComponent,
  HealingChangeComponent,
  ComboComponent,
  ModalInfoSpecialSkillsComponent,
  AiRevisorModalComponent,
  ChaosComponent,
  ChaosTableComponent,
  RpcComponent,
  MODMoonAttributesComponent,
  ModMoonRangesComponent,
  ParryPryComponent,
  DestinyDiceComponent,
  ViewSendModalComponent
];

@NgModule({
  imports: [
    CommonModule,
    BrowserModule,
    FormsModule,
    RouterModule,
    RouterModule.forRoot(routes,
      {
        onSameUrlNavigation: 'reload',
        scrollPositionRestoration: 'enabled',
      }),
    LevelAndDialogueModule,
    MissionListModule,
    ReportViewModule,
    RpgManagerModule,
    SettingsModule,
    MajorModule,
    ContentInputModule,
    PopupModule,
    HttpClientModule,
    AudioListenerModule,
    PiecesModule
  ],
  declarations: [routingComponents],
  exports: [RouterModule, routingComponents],
})
export class AppRoutingModule { }

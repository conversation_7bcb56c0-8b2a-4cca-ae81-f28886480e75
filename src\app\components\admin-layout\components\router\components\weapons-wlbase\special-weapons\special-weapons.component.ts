import { ChangeDetectorRef, Component, EventEmitter, OnInit, Output } from '@angular/core';
import { AreaService } from 'src/app/services/area.service';
import { ItemClassService } from 'src/app/services/item-class.service';
import { ReviewService } from 'src/app/services/review.service';
import { ItemService } from 'src/app/services/item.service';
import { Item, SpecialWeapon } from 'src/app/lib/@bus-tier/models';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { CustomService } from 'src/app/services/custom.service';
import { SpecialWeaponService } from 'src/app/services/special-weapon.service';
import { ISpecialWeaponsHC } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';
import { Alert, Review } from 'src/lib/darkcloud';
import { Area } from 'src/app/lib/@bus-tier/models';
import { ItemClass } from 'src/app/lib/@bus-tier/models/ItemClass';
import { SpecialWeaponServiceHC } from 'src/app/services/special-weaponHC.service';


interface Itemlevel {
  hc?: number;
  idCircle?: string;
  nameCircle?: string;
  receivedAt?: string[];
  idNameSW?:string;
  idNameIngredient?:string;
}

@Component({
  selector: 'app-special-weapons',
  templateUrl: './special-weapons.component.html',
  styleUrls: ['./special-weapons.component.scss']
})
export class SpecialWeaponsComponent implements OnInit{

  valueRange: string;
  blueprints: Item[];
  itemIds: string[];
  listItem: Itemlevel[];
  specialWeapons: SpecialWeapon[];
  hcSpecialWeapons: ISpecialWeaponsHC[] = [];
  listHCSpecialWeapons: ISpecialWeaponsHC[] = [];
  toCreateSpecialWeapons: ISpecialWeaponsHC[] = [];
  uniqueIngredients: Item[];
  selectedClasses = [];
  itemClasses: ItemClass[] = [];
  listCommon: Itemlevel[] = [];
  ids = [];
  custom: Custom;
  sortNameOrder = +1;

  constructor(
    protected _customService: CustomService,
    private _itemClassService: ItemClassService,
    private _itemService: ItemService,
    private _specialWeaponService: SpecialWeaponService,
    private _specialWeaponServiceHC: SpecialWeaponServiceHC,
    private _areaService: AreaService,
    private _reviewService: ReviewService,
    private _change: ChangeDetectorRef,
  ) {}


  async ngOnInit(): Promise<void>  {
    this._customService.toFinishLoading();
    this.custom = await this._customService.svcGetInstance(); 
    this.itemClasses = this._itemClassService.models.filter((x) => x.name === "BLUEPRINTS"); 
    
    if(this.itemClasses.length > 0) {
      this.itemIds = this.itemClasses[0]?.itemIds;
      this.selectClasses(); 
      this.updateSpecialWeapons();
      await this._specialWeaponServiceHC.toFinishLoading();  
    
      this.listHCSpecialWeapons = this._specialWeaponServiceHC.models;
      this.lineupOrderCommon();  
    }
    else {
      Alert.showError('The item class name "BLUEPRINTS" was not found. Please check the Item Classes.');
    } 
  }


  updateSpecialWeapons() {
    this.toCreateSpecialWeapons = this.filterAndAssignSpecialWeapons();
    this.listHCSpecialWeapons = this._specialWeaponServiceHC.models;

    // Verifica se há conteúdo em listHCSpecialWeapons
    if (this.listHCSpecialWeapons.length > 0) {
        // Itera sobre os itens de toCreateSpecialWeapons para criar novos itens se necessário
        this.toCreateSpecialWeapons.forEach(item => {
            // Verifica se o item tem idCircle correspondente em listHCSpecialWeapons
            const matchingHCSpecialWeapon = this.listHCSpecialWeapons.find(hcItem => hcItem.idCircle === item.idCircle);

            if (matchingHCSpecialWeapon) {
                // Se houver um item com idCircle correspondente, comparar os idNameBPR em bluePrintReceivedHC
                item.bluePrintReceivedHC?.forEach(itemBP => {
                    const matchingBP = matchingHCSpecialWeapon.bluePrintReceivedHC?.find(hcBP => hcBP.idNameBPR === itemBP.idNameBPR);
                    // Se idNameBPR for diferente ou não encontrado, criar um novo SpecialWeaponHC
                    if (!matchingBP) {                                
                      this._specialWeaponServiceHC.addNewBluePrintReceivedHC(item);
                    }
                });
                // Remover bluePrintReceivedHC se idNameBPR não existir em toCreateSpecialWeapons
                const blueprintToRemove = matchingHCSpecialWeapon.bluePrintReceivedHC?.filter(hcBP =>
                    !item.bluePrintReceivedHC?.some(itemBP => itemBP?.idNameBPR === hcBP?.idNameBPR)
                );

                if (blueprintToRemove && blueprintToRemove.length > 0) {
                    blueprintToRemove.forEach(bpToRemove => {
                        // Removendo os itens do bluePrintReceivedHC que não estão mais presentes
                        const indexToRemove = matchingHCSpecialWeapon.bluePrintReceivedHC?.findIndex(hcBP => hcBP.idNameBPR === bpToRemove.idNameBPR);
                        if (indexToRemove !== undefined && indexToRemove !== -1) {
                            matchingHCSpecialWeapon.bluePrintReceivedHC?.splice(indexToRemove, 1);
                        }
                    });
                }

            } else {
                // Se o idCircle não for encontrado, criar um novo SpecialWeaponHC
                this._specialWeaponServiceHC.createNewSpecialWeaponHC(item);
            }
        });

        // Remover toda a estrutura de hcSpecialWeapons se o idCircle não existir em toCreateSpecialWeapons
        const itemsToRemove = this.listHCSpecialWeapons.filter(hcItem =>
            !this.toCreateSpecialWeapons.some(item => item.idCircle === hcItem.idCircle)
        );

        if (itemsToRemove && itemsToRemove.length > 0) {
            itemsToRemove.forEach(itemToRemove => {
                this._specialWeaponServiceHC.svcToRemove(itemToRemove.id);
            });
        }

    } else {
        // Se não houver conteúdo em listHCSpecialWeapons, criar todos os itens de toCreateSpecialWeapons
        this.toCreateSpecialWeapons.forEach(item => {
            this._specialWeaponServiceHC.createNewSpecialWeaponHC(item);
        });
    }
}

  selectClasses() 
  {
    this.selectedClasses = this._itemClassService.models.filter((klass) => 
    {
      return this.custom.specialWeaponClassItem.includes(klass.id);
    });
    let itemIds = [];    
    
    this.selectedClasses.forEach((itemClass) => 
    {
      itemClass.itemIds.forEach((itemId) => 
      {
        itemIds.push(this._itemService.svcFindById(itemId));
      });
    });
    
    this.ids = [];
    itemIds.forEach((id) => 
    {
      this.ids.push(id?.id)
    });
    
    this.specialWeapons = [];

    this._specialWeaponService.models.forEach((sw) =>
    {
      if(this.ids.includes(sw.itemId))
      {
        this.specialWeapons.push(sw);
        this.ids = this.ids.filter(id => id !== sw.itemId);
      }     
    });
   
    this.ckeckCircleWeapon();
    this.getHCSpecialWeapons();  
  }

  ckeckCircleWeapon() {
 
    //Pega os dados das armas
    this.itemIds.forEach((item) => {
      const valor = this._itemService.models.filter((op)=> item == op.id);
      this.listItem =  valor.map((x) => ({idNameSW: x.id,nameSW: x.name, receivedAt: this.validatesItemInUse(x).receivedAt, typeItem: (x?.type != undefined ? x.type.toLocaleString() : x.type)})); 
       this.listItem.forEach((m) => {
        if(m.receivedAt.length > 0) this.listCommon.push(m)
      });       
    });

   //Verifica o nome do circulo
    this.listCommon.map((x) => {
      if(x.receivedAt.length > 0) {
        for (let index = 0; index < x.receivedAt.length; index++) {
          const IdCircle = Area.getSubIdFrom(x.receivedAt[index]);   
          const local = this._areaService.svcFindById(IdCircle); 
          x.idCircle = local.id;
          x.nameCircle = local.name;
          x.hc = local.order;          
        }
      }
    });   
  }

  //validates if item is Received
  validatesItemInUse(obj: any): Review.Result {
      if (!obj) return null;  
       let reviewsItem = this._reviewService.reviewResults[obj.id]; 
      return reviewsItem;
    } 

  getHCSpecialWeapons() {   

  this.hcSpecialWeapons = [];
  this.specialWeapons.forEach(sw => {
    const hcWeapon: ISpecialWeaponsHC = {
      bluePrintReceivedHC: [
        {
          idSW: sw.itemId,
          idNameSW: sw.id,// para pegar o nome do SPECIAL WEAPON
          idNameBPR: sw.bpId, // para pegar o nome do Blue Print e depois comparar com o id do this.blueprints
          idNameIngredient: sw.ingredientId, // para pegar o nome do Ingredient e depois comparar com o id do this.uniqueIngredients
        }
      ],
        name: '',
        id: '',
        idCircle: ''
      };
      this.hcSpecialWeapons.push(hcWeapon);
      });   
  }


  filterAndAssignSpecialWeapons(): ISpecialWeaponsHC[] {
    const foundSpecialWeapons: ISpecialWeaponsHC[] = [];

    this.listCommon.forEach(commonItem => { //36
        this.hcSpecialWeapons.forEach(specialWeapon => { //60
            if (specialWeapon.bluePrintReceivedHC) {
          
                let found = false;
                specialWeapon.bluePrintReceivedHC.forEach(blueprint => {                 
                    if (commonItem?.idNameSW === blueprint?.idNameBPR) {
                        specialWeapon.name = commonItem.nameCircle;
                        specialWeapon.idCircle = commonItem.idCircle;
                        specialWeapon.hc = commonItem.hc;
                        found = true;
                    }
                });

                if (found) {               
                    foundSpecialWeapons.push(specialWeapon);
                }
            }
        });
    });

    return this.mergeBlueprintsByIdCircle(foundSpecialWeapons);
}

  mergeBlueprintsByIdCircle(foundSpecialWeapons: ISpecialWeaponsHC[]): ISpecialWeaponsHC[] {
      const mergedSpecialWeapons: { [key: string]: ISpecialWeaponsHC } = {};

      foundSpecialWeapons.forEach(specialWeapon => {
          if (specialWeapon.idCircle) {
              if (!mergedSpecialWeapons[specialWeapon.idCircle]) {
                  // Se ainda não existe uma entrada para esse idCircle, cria uma nova
                  mergedSpecialWeapons[specialWeapon.idCircle] = {
                      ...specialWeapon,
                      bluePrintReceivedHC: [...(specialWeapon.bluePrintReceivedHC || [])]
                  };
              } else {
                  // Se já existe uma entrada para esse idCircle, mescla os blueprints
                  mergedSpecialWeapons[specialWeapon.idCircle].bluePrintReceivedHC.push(
                      ...(specialWeapon.bluePrintReceivedHC || [])
                  );
              }
          }
      });
  
      return Object.values(mergedSpecialWeapons);
  }

  GetBluePrintName(idNameBPR: string) {
    return this._itemService.svcFindById(idNameBPR)?.name;
  }  

  GetSpecialWeaponName(memoryModuleId: string): string 
  {
    return this._itemService.svcFindById(this._specialWeaponService.svcFindById(memoryModuleId)?.itemId)?.name;
  }

  GetIngredient(ingredId: string) {
    return this._itemService.svcFindById(ingredId)?.name;
  }

lineupOrderCommon() 
{
this.sortNameOrder *= +1;
  this.listHCSpecialWeapons.sort((a, b) => 
  {  
    return this.sortNameOrder * a.name.localeCompare(b.name);
  });
}

getWlRangeClass(hc: number): string {
  const roundedHc = Math.floor(hc); 

  if (roundedHc >= 0 && roundedHc <= 2) {
    this.valueRange = "1 - 6";  
      return 'green-bg'; 
  } else if (roundedHc >= 3 && roundedHc <= 5) {
    this.valueRange = "7 - 12"; 
      return 'primary-bg'; 
  } else if (roundedHc >= 6 && roundedHc <= 7) {
    this.valueRange = "13 - 16";
      return 'purple-bg'; 
  } else if (roundedHc >= 8 && roundedHc <= 9) {
    this.valueRange = "17 - 20";
      return 'yellow-bg'; 
  } else {
    this.valueRange = '';
    return '';
  }  
  
}

onChangeCommonWeapons(
  comm: any, 
  value: string, 
  fieldName: string, 
  index: number
) {

  // Verifique e defina os valores adequados usando os setters da classe SpecialWeaponsHC
  if (fieldName === 'qi_Min') {
    const bluePrintReceivedHC = comm.bluePrintReceivedHC;
    if (bluePrintReceivedHC && bluePrintReceivedHC[index]) {
      bluePrintReceivedHC[index].qi_Min = value === '' ? undefined : +value;
      comm.bluePrintReceivedHC = bluePrintReceivedHC;
    }
  }

  if (fieldName === 'luck_min') {
    const bluePrintReceivedHC = comm.bluePrintReceivedHC;
    if (bluePrintReceivedHC && bluePrintReceivedHC[index]) {
      bluePrintReceivedHC[index].luck_min = value === '' ? undefined : +value;
      comm.bluePrintReceivedHC = bluePrintReceivedHC;
    }
  }

  if (fieldName === 'wlBase') {
    const bluePrintReceivedHC = comm.bluePrintReceivedHC;
    if (bluePrintReceivedHC && bluePrintReceivedHC[index]) {
      bluePrintReceivedHC[index].wlBase = value === '' ? undefined : +value;
      comm.bluePrintReceivedHC = bluePrintReceivedHC;
    }
  }

  this._specialWeaponServiceHC.svcToModify(comm);
  this._specialWeaponServiceHC.toSave();   
}

sortNameOrderCommon: number = +1; 
orderSortBluePrint() {
    this.sortNameOrderCommon *= -1;
     this.listHCSpecialWeapons.forEach(comm => {
        comm.bluePrintReceivedHC.sort((a, b) => {
         return this.sortNameOrderCommon * a.idNameBPR.localeCompare(b.idNameBPR)
     });
    });  
    this._change.detectChanges();
}

sortNameOrderHC = +1;

orderSortCommonHC() {
  this.sortNameOrderHC *= -1;   
  this.listHCSpecialWeapons.sort((a, b) => {
    return this.sortNameOrderHC * (a.hc - b.hc);
  });
}

}
